import foodPartnerModel from "../model/foodpartner.model.js";
import foodModal from "../model/food.model.js";


export const getFoodPartnerById = async (req, res) => {
    const foodPartnerId = req.params.id;

    const foodPartner = await foodPartnerModel.findById(foodPartnerId)
    const foodItemsByGoodPatner = await foodModal.find({foodPartner: foodPartnerId})
    if(!foodPartner){
        return res.status(404).json({
            message: "Food partner not found"
        })
    }
    res.status(200).json({
        message: "Food partner fetched successfully",
        foodPartner:{
            ...foodPartner.toObject(),
            foodItems: foodItemsByGoodPatner
        }
        
    })
}