import { uploadImage } from "../../service/storage.service.js";
import foodModal from "../model/food.model.js";
import { v4 as uuidv4 } from "uuid";
import likeModal from "../model/like.model.js";
import saveFoodModal from "../model/save.modal.js";


export const createFood = async (req, res) => {
    try {

        const { name, discription } = req.body;
        // console.log(req.foodPartner)
        // console.log(req.body)
        // console.log(req.file)

        const fileUploadResult = await uploadImage(req.file.buffer, uuidv4());

        const foodItems = await foodModal.create({
            name,
            video: fileUploadResult.url,
            discription,
            foodPartner: req.foodPartner._id
        })
        res.status(201).json({
            message: "Food created successfully",
            food: foodItems,
        })

    } catch (error) {
        console.error("Error creating food:", error);
        res.status(500).json({ message: error.message });
    }
}


export const getFoodVideo = async (req, res) => {
    const foodItems = await foodModal.find({})
    console.log(foodItems)
    res.status(200).json({
        message: "Food fetched successfully",
        food: foodItems,
    })
}


export const likeFood = async (req, res) => {
  try {
    const { foodId } = req.body;
    const user = req.user;

    const isAlreadyLiked = await likeModal.findOne({
      user: user._id,
      food: foodId
    });

    if (isAlreadyLiked) {
      await likeModal.deleteOne({ user: user._id, food: foodId });
      await foodModal.findByIdAndUpdate(foodId, { $inc: { likesCount: -1 } });

      return res.status(200).json({ message: "Food unliked successfully" });
    }

    const like = await likeModal.create({ user: user._id, food: foodId });

    await foodModal.findByIdAndUpdate(foodId, { $inc: { likesCount: 1 } });

    res.status(201).json({ message: "Food liked successfully", like });

  } catch (error) {
    console.error("Error liking food:", error);
    res.status(500).json({ message: error.message });
  }
};


export const saveFood = async (req, res) => {
  try {
    const { foodId } = req.body;
    const user = req.user;

    const isAlreadySaved = await saveFoodModal.findOne({
      user: user._id,
      food: foodId
    });

    if (isAlreadySaved) {
      await saveFoodModal.deleteOne({ user: user._id, food: foodId });
      await foodModal.findByIdAndUpdate(foodId, { $inc: { savesCount: -1 } });

      return res.status(200).json({ message: "Food unsaved successfully" });
    }

    const save = await saveFoodModal.create({ user: user._id, food: foodId });

    await foodModal.findByIdAndUpdate(foodId, { $inc: { savesCount: 1 } });

    res.status(201).json({ message: "Food saved successfully", save });

  } catch (error) {
    console.error("Error saving food:", error);
    res.status(500).json({ message: error.message });
  }
};
