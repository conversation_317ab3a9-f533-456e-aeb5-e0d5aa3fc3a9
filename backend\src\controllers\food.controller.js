import { uploadImage } from "../../service/storage.service.js";
import foodModal from "../model/food.model.js";
import { v4 as uuidv4 } from "uuid";


export const createFood = async (req, res) => {
    try {

        const { name, discription } = req.body;
        // console.log(req.foodPartner)
        // console.log(req.body)
        // console.log(req.file)

        const fileUploadResult = await uploadImage(req.file.buffer, uuidv4());

        const foodItems = await foodModal.create({
            name,
            video: fileUploadResult.url,
            discription,
            foodPartner: req.foodPartner._id
        })
        res.status(201).json({
            message: "Food created successfully",
            food: foodItems,
        })

    } catch (error) {
        console.error("Error creating food:", error);
        res.status(500).json({ message: error.message });
    }
}


export const getFoodVideo = async (req, res) => {
    const foodItems = await foodModal.find({})
    console.log(foodItems)
    res.status(200).json({
        message: "Food fetched successfully",
        food: foodItems,
    }) 
}