import { uploadImage } from "../../service/storage.service.js";
import foodPartnerModel from "../model/foodpartner.model.js";
import {v4 as uuidv4} from "uuid";


export const createFood = async (req, res) => {
    // console.log(req.foodPartner)
    // console.log(req.body)
    // console.log(req.file)

    const fileUploadResult = await uploadImage(req.file.buffer, uuidv4())
    console.log(fileUploadResult)
    res.send({message: "Food created"})
}
