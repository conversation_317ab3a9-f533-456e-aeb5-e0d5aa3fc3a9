import monsgoose from "mongoose";

const userSchema = new monsgoose.Schema({
    fullName: {
        type: String,
        required: true,
    },
    email: {
        type: String,
        required: true,
        unique: true,
    },
    password: {
        type: String,
        required: true,
    },
},{
    timestamps: true,
})

const User = monsgoose.model("User", userSchema);

export default User;