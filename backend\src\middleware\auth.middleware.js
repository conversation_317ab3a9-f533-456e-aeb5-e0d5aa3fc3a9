import foodPartnerModel from "../model/foodpartner.model.js"
import jwt from "jsonwebtoken"

export const authFoodPartnerMiddleware = async (req, res, next) => {
    const token  = req.cookies.token
    // console.log(token)
    if(!token){
        return res.status(401).json({
            message: "Unauthorized"
        })
    }

    try {
      const decoded =   jwt.verify(token , process.env.JWT_SECRET)
      const foodPartner = await foodPartnerModel.findById(decoded.id)
      if(!foodPartner) return res.status(401).json({
            message: "Unauthorized"
        })
        req.foodPartner = foodPartner
        next()
    } catch (error) {
        console.log(error)
        return res.status(401).json({
            message: "Invalid token"
        })
    }
}