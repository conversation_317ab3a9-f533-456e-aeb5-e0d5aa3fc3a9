import React from "react";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";

import Home from "../pages/general/Home";
import CreateFood from "../pages/createfood/CreateFood";
import UserLogin from "../pages/auth/user/UserLogin";
import PartnerRegister from "../pages/auth/partner/PartnerRegister";
import PartnerLogin from "../pages/auth/partner/PartnerLogin";
import UserRegister from "../pages/auth/user/UserRegister";
import FoodPatner from "../pages/foodpartner_profile_page/FoodPatner";

const AppRoutes = () => {
  return (
    <Router>
      <Routes>
        <Route path="/user/register" element={<UserRegister/>} />
        <Route path="/user/login" element={<UserLogin />} />
        <Route path="/food-partner/register" element={<PartnerRegister />} />
        <Route path="/food-partner/login" element={<PartnerLogin />} />
        <Route path="/food" element={<h1>Food</h1>} />

        <Route path="/" element={<Home/>} />
        <Route path="/create-food" element={<CreateFood/>} />
        <Route path="/food-partner/:id" element={<FoodPatner/>} />

      </Routes>
    </Router>
  );
};

export default AppRoutes;
