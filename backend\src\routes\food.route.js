import express from "express";
import multer from "multer";
import { createFood, getFoodVideo } from "../controllers/food.controller.js";
import { authFoodPartnerMiddleware, authUserMiddleware } from "../middleware/auth.middleware.js";
const router = express.Router();
const upload = multer({
    storage: multer.memoryStorage() 
})

router.post('/' , authFoodPartnerMiddleware , upload.single('video') ,createFood)
router.get('/' , authUserMiddleware ,getFoodVideo)

export default router;
