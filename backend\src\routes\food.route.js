import express from "express";
import multer from "multer";
import { createFood } from "../controllers/food.controller.js";
import { authFoodPartnerMiddleware } from "../middleware/auth.middleware.js";
const router = express.Router();
const upload = multer({
    storage: multer.memoryStorage() 
})

router.post('/' , authFoodPartnerMiddleware , upload.single('video') ,createFood)

export default router;
