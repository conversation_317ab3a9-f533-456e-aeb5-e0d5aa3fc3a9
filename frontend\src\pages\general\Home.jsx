import axios from "axios";
import React, { useEffect, useRef, useState } from "react";
import { Link } from "react-router-dom";
import { Heart, Bookmark } from "lucide-react"; // ✅ Lucide icons

const Home = () => {
    const containerRef = useRef(null);
    const videoRefs = useRef([]);

    const [data, setData] = useState([]);

    // Intersection Observer to auto-play videos
    useEffect(() => {
        if (!containerRef.current) return;
        if (!data || data.length === 0) return;

        videoRefs.current = videoRefs.current.slice(0, data.length);

        const observer = new IntersectionObserver(
            (entries) => {
                entries.forEach((entry) => {
                    const vid = entry.target;
                    if (entry.isIntersecting && entry.intersectionRatio >= 0.75) {
                        vid.play().catch(() => { });
                    } else {
                        vid.pause();
                    }
                });
            },
            {
                root: containerRef.current,
                threshold: [0.75],
            }
        );

        videoRefs.current.forEach((v) => {
            if (v) observer.observe(v);
        });

        return () => {
            observer.disconnect();
        };
    }, [data]);

    // Fetch food videos
    useEffect(() => {
        async function fetchData() {
            const response = await axios.get("http://localhost:3000/api/food", {
                withCredentials: true,
            });

            const foodItems = response.data.food || [];
            console.log(foodItems)
            setData(foodItems);
        }
        fetchData();
    }, []);

    const handleLike = async (foodId) => {
        try {
            const response = await axios.post("http://localhost:3000/api/food/like", { foodId }, { withCredentials: true });
            // server should return the updated likesCount (number)
            const serverCount = typeof response.data.likesCount === 'number' ? response.data.likesCount : null;
            if (serverCount !== null) {
                setData((prev) =>
                    prev.map((item) =>
                        item._id === foodId ? { ...item, likesCount: serverCount } : item
                    )
                );
            } else {
                // fallback: toggle locally without allowing negative values
                setData((prev) =>
                    prev.map((item) =>
                        item._id === foodId
                            ? { ...item, likesCount: Math.max(0, (item.likesCount || 0) + 1) }
                            : item
                    )
                );
            }
        } catch (error) {
            console.log(error)
        }
    }

    return (
        <div
            ref={containerRef}
            className="h-screen w-full overflow-y-scroll snap-y snap-mandatory touch-pan-y"
            style={{ scrollSnapType: "y mandatory" }}
        >
            {data.map((item, idx) => (
                <section
                    key={item._id}
                    className="snap-start h-screen w-full relative bg-black flex items-center justify-center"
                >
                    {/* Video */}
                    <video
                        ref={(el) => (videoRefs.current[idx] = el)}
                        src={item.video}
                        className="w-full h-full object-cover"
                        muted
                        playsInline
                        loop
                        preload="metadata"
                    />

                    {/* Overlay at bottom */}
                    <div className="absolute left-4 right-4 bottom-6 z-20 flex flex-col gap-3">
                        <p
                            className="text-white text-lg font-medium drop-shadow"
                            style={{
                                display: "-webkit-box",
                                WebkitLineClamp: 2,
                                WebkitBoxOrient: "vertical",
                                overflow: "hidden",
                            }}
                        >
                            {item.discription}
                        </p>

                        <div className="flex">
                            <Link
                                to={`/food-partner/${item.foodPartner}`}
                                className="bg-white/90 text-black px-4 py-2 rounded-md font-semibold shadow-md hover:bg-white"
                            >
                                Visit Store
                            </Link>
                        </div>
                    </div>

                    {/* Right-side icons (Like + Save with counts) */}
                    <div className="absolute right-4 bottom-28 flex flex-col items-center gap-6 z-20">
                        {/* Like */}
                        <div onClick={() => handleLike(item._id)} className="flex flex-col items-center text-white">
                            <Heart size={28} />
                            <span className="text-sm">{item.likesCount || 0}</span>
                        </div>

                        {/* Save */}
                        <div onClick={() => handleSave(item._id)} className="flex flex-col items-center text-white">
                            <Bookmark size={28} />
                            <span className="text-sm">{item.savesCount || 0}</span>
                        </div>
                    </div>

                    {/* Bottom gradient */}
                    <div className="absolute bottom-0 left-0 right-0 h-28 bg-gradient-to-t from-black/70 to-transparent pointer-events-none" />
                </section>
            ))}
        </div>
    );
};

export default Home;
