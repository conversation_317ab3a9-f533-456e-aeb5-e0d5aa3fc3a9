import app from "./src/app.js"
import dotenv from "dotenv";
import { connectDB } from "./src/db/db.config.js";
import path from "path";
import { fileURLToPath } from "url";



const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

dotenv.config({ path: path.join(__dirname, '.env') });
connectDB();

app.listen(3000, () => {
  console.log("Server is running on port 3000");
});
