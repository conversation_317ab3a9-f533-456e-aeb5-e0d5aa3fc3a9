import axios from "axios";
import React, { useState } from "react";
import { Link, useNavigate } from "react-router-dom";

const PartnerLogin = () => {
  const navigate = useNavigate();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const submitHandler = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    const formdata = { email, password };

    try {
      const response = await axios.post(
        "http://localhost:3000/api/auth/food-partner/login",
        formdata,
        { withCredentials: true }
      );
      console.log(response.data);

      // Reset form
      setEmail("");
      setPassword("");

      // Navigate after login
      navigate("/create-food")
    } catch (err) {
      setError(err.response?.data?.message || "Something went wrong");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 text-gray-900 dark:bg-neutral-950 dark:text-gray-100">
      <div className="w-full max-w-md rounded-2xl border border-gray-200/70 bg-white/80 p-8 shadow-xl backdrop-blur-sm dark:border-neutral-800 dark:bg-neutral-900/80">
        <div className="mb-6 text-center">
          <h1 className="text-2xl font-semibold tracking-tight">Partner login</h1>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Access your partner dashboard
          </p>
        </div>

        <form onSubmit={submitHandler} className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1" htmlFor="email">
              Business email
            </label>
            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="<EMAIL>"
              className="w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm outline-none transition 
              focus:border-teal-500 focus:ring-2 focus:ring-teal-500/20 
              dark:border-neutral-700 dark:bg-neutral-800"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1" htmlFor="password">
              Password
            </label>
            <input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="••••••••"
              className="w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm outline-none transition 
              focus:border-teal-500 focus:ring-2 focus:ring-teal-500/20 
              dark:border-neutral-700 dark:bg-neutral-800"
            />
          </div>

          {error && (
            <p className="text-sm text-red-500 dark:text-red-400">{error}</p>
          )}

          <button
            type="submit"
            disabled={isLoading}
            className="mt-2 w-full rounded-lg bg-teal-600 px-4 py-2.5 text-sm font-medium text-white 
            transition hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-teal-400/40 
            disabled:opacity-70 disabled:cursor-not-allowed flex items-center justify-center gap-2"
          >
            {isLoading ? (
              <>
                <svg
                  className="h-4 w-4 animate-spin"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8v4l3-3-3-3v4a12 12 0 00-12 12h4z"
                  ></path>
                </svg>
                Logging in...
              </>
            ) : (
              "Log in"
            )}
          </button>
        </form>

        <p className="mt-6 text-center text-sm text-gray-600 dark:text-gray-400">
          New partner?{" "}
          <Link
            to="/food-partner/register"
            className="font-medium text-teal-600 hover:underline dark:text-teal-400"
          >
            Create an account
          </Link>
        </p>
      </div>
    </div>
  );
};

export default PartnerLogin;
