import mongoose from "mongoose";

const foodSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true
    },
    video:{
        type: String,
        required: true
    },
    discription: {
        type: String,
        required: true
    },
    foodpath: {
        type:mongoose.Schema.Types.ObjectId,
        ref:"foodpartner",
        required: true
    }
})

const food = mongoose.model("food", foodSchema);

export default food;