{"name": "backend", "version": "1.0.0", "description": "", "main": "app.js", "scripts": {"start": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "dependencies": {"bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "dotenv": "^17.2.2", "express": "^5.1.0", "imagekit": "^6.0.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.18.1", "multer": "^2.0.2", "nodemon": "^3.1.10", "uuid": "^13.0.0"}}