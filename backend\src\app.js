import express from "express";
import authRoutes from "./routes/auth.routes.js";
import foodRoutes from './routes/food.route.js'
import foodPatnersRoutes from "./routes/food-partners.routes.js"
import cookieParser from "cookie-parser";
import cors from 'cors'

const corsOptions = {
  origin: 'http://localhost:5173',
  credentials: true,
};


const app = express();
app.use(cors(corsOptions));

app.use(express.json());
app.use(cookieParser());
app.use(express.urlencoded({ extended: true }));
app.use("/api/auth", authRoutes);
app.use("/api/food", foodRoutes);
app.use("/api/food-partner", foodPatnersRoutes);

app.get("/", (req, res) => {
  res.send("Hello World!");
});

export default app;