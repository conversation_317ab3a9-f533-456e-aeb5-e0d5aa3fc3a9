import express from "express";
import authRoutes from "./routes/auth.routes.js";
import foodRoutes from './routes/food.route.js'
import cookieParser from "cookie-parser";



const app = express();

app.use(express.json());
app.use(cookieParser());
app.use(express.urlencoded({ extended: true }));
app.use("/api/auth", authRoutes);
app.use("/api/food", foodRoutes);

app.get("/", (req, res) => {
  res.send("Hello World!");
});

export default app;