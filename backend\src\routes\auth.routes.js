import express from 'express';
import { <PERSON>gin<PERSON>oodPartneder, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ut<PERSON>oodPartneder, <PERSON><PERSON>ut<PERSON>ser, RegisterFoodPartneder, RegisterUser } from '../controllers/auth.controller.js';

const router = express.Router();

router.post('/user/register' , RegisterUser);
router.post('/user/login' , LoginUser);
router.get("/user/logout" , LogoutUser)

router.post("/food-partner/register" , RegisterFoodPartneder);
router.post("/food-partner/login" , LoginFoodPartneder);
router.get('/food-partner/logout' , LogoutFoodPartneder);




export default router;