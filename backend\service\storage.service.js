import ImageKit from "imagekit";

let imagekit;

const initializeImageKit = () => {
    if (!imagekit) {
        imagekit = new ImageKit({
            publicKey: process.env.IMAGEKIT_PUBLIC_KEY,
            privateKey: process.env.IMAGEKIT_PRIVATE_KEY,
            urlEndpoint: process.env.IMAGEKIT_URL_ENDPOINT,
        });
    }
    return imagekit;
};

export const uploadImage = async (file, fileName) => {
    try {
        const imagekitInstance = initializeImageKit();
        const result = await imagekitInstance.upload({
            file: file,
            fileName: fileName,
        });
        return result;
    } catch (error) {
        throw new Error(`Image upload failed: ${error.message}`);
    }
}