import ImageKit from "imagekit";

const imagekit = new ImageKit({
    publicKey : process.env.IMAGEKIT_PUBLIC_KEY,
    privateKey : process.env.IMAGEKIT_PRIVATE_KEY,
    urlEndpoint : process.env.IMAGEKIT_URL_ENDPOINT,
})

export const uploadImage = async (file , flielName) => {
    try {
        const result = await imagekit.upload({
            file: file,
            fileName: flielName,
        })
        return result;
    } catch (error) {
        res.status(500).json({message: error.message})
    }
}