import React, { useEffect, useState } from "react";
import { Play, MapPin, Users, ChefHat, Star } from "lucide-react";
import { useParams } from "react-router-dom";
import axios from "axios";

const FoodPartner = () => {
  const { id } = useParams();
  const [profileData, setProfileData] = useState(null);
  const [video, setVideo] = useState([])

  useEffect(() => {
    async function fetchFoodPartner() {
      try {
        const response = await axios.get(
          `http://localhost:3000/api/food-partner/${id}`,
          { withCredentials: true }
        );
        console.log(response.data.foodPartner.foodItems);
        setProfileData(response.data.foodPartner);
        setVideo(response.data.foodPartner.foodItems)
      } catch (err) {
        console.log(err);
      }
    }
    fetchFoodPartner();
  }, [id]);

  if (!profileData) {
    return (
      <div className="flex justify-center items-center h-screen">
        <p className="text-gray-600">Loading...</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-gray-100 p-4">
      <div className="max-w-sm mx-auto">
        {/* Profile Header Card */}
        <div className="bg-white rounded-3xl shadow-2xl border-0 mb-6 overflow-hidden">
          {/* Header Background */}
          <div className="h-20 bg-gradient-to-r from-orange-400 via-red-500 to-pink-500 relative">
            <div className="absolute inset-0 bg-black bg-opacity-10"></div>
          </div>

          <div className="px-6 pb-6 -mt-8 relative">
            {/* Avatar */}
            <div className="flex justify-center mb-4">
              <div className="w-20 h-20 rounded-2xl bg-gradient-to-br from-orange-500 to-red-600 flex items-center justify-center border-4 border-white shadow-lg">
                <span className="text-white text-lg font-bold">
                  {profileData.name?.substring(0, 2).toUpperCase()}
                </span>
              </div>
            </div>

            {/* Restaurant Info */}
            <div className="text-center mb-6">
              <h1 className="text-xl font-bold text-gray-900 mb-2">
                {profileData.name}
              </h1>

              <div className="flex items-center justify-center text-gray-600 mb-4">
                <MapPin className="w-4 h-4 mr-2 text-red-500" />
                <p className="text-sm leading-relaxed">
                  {profileData.address || "Address not available"}
                </p>
              </div>

              {/* Rating */}
              <div className="flex items-center justify-center mb-4">
                <div className="flex items-center bg-yellow-50 px-3 py-1 rounded-full">
                  <Star className="w-4 h-4 text-yellow-500 fill-current mr-1" />
                  <span className="text-sm font-semibold text-gray-800">
                    {profileData.rating || "4.8"}
                  </span>
                  <span className="text-xs text-gray-600 ml-1">
                    ({profileData.reviews || "0"} reviews)
                  </span>
                </div>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-4 border border-blue-100">
                  <div className="text-center">
                    <div className="flex items-center justify-center mb-2">
                      <ChefHat className="w-5 h-5 text-blue-600 mr-1" />
                    </div>
                    <div className="text-lg font-bold text-gray-900">
                      {profileData.totalMeals?.toLocaleString() || 0}
                    </div>
                    <div className="text-xs text-gray-600 font-medium">
                      Total Meals
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl p-4 border border-green-100">
                  <div className="text-center">
                    <div className="flex items-center justify-center mb-2">
                      <Users className="w-5 h-5 text-green-600 mr-1" />
                    </div>
                    <div className="text-lg font-bold text-gray-900">
                      {profileData.customerServe || 0}
                    </div>
                    <div className="text-xs text-gray-600 font-medium">
                      Happy Customers
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Videos Section */}
        <div className="bg-white rounded-3xl shadow-2xl border-0 overflow-hidden">
          <div className="p-6">
            <div className="flex items-center justify-between mb-5">
              <h2 className="text-lg font-bold text-gray-900">Kitchen Stories</h2>
              <div className="flex items-center text-sm text-gray-600">
                <Play className="w-4 h-4 mr-1" />
                <span>{video.length} videos</span>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              {video.map((video) => (
                <div
                  key={video._id}
                  className="group cursor-pointer rounded-2xl overflow-hidden shadow-lg bg-gray-100 transition-all duration-300 group-hover:shadow-xl group-hover:scale-105"
                >
                  <video
                    src={video.video}
                    className="w-full h-[30vh] object-cover"
                    controls
                  />
                  <h3 className="text-gray-900 text-sm font-semibold mt-3 py-2 leading-tight px-1">
                    {video.discription}
                  </h3>
                </div>
              ))}
            </div>
          </div>
        </div>


        {/* Debug ID - Styled */}
        <div className="mt-6 text-center">
          <div className="inline-flex items-center bg-white/60 backdrop-blur-sm px-3 py-1 rounded-full border border-gray-200">
            <span className="text-xs text-gray-500 font-mono">
              ID: {id || "N/A"}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FoodPartner;
