import axios from 'axios'
import React, { useState, useRef, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'

const CreateFood = () => {
  const navigate = useNavigate()
  const [name, setName] = useState('')
  const [description, setDescription] = useState('')
  const [file, setFile] = useState(null)
  const [preview, setPreview] = useState(null)
  const [status, setStatus] = useState('Ready to create')
  const fileRef = useRef(null)
  const prevUrlRef = useRef(null)

  useEffect(() => {
    // revoke previous object URL when preview changes or component unmounts
    return () => {
      if (prevUrlRef.current) URL.revokeObjectURL(prevUrlRef.current)
    }
  }, [])

  useEffect(() => {
    if (prevUrlRef.current && prevUrlRef.current !== preview) {
      URL.revokeObjectURL(prevUrlRef.current)
    }
    prevUrlRef.current = preview
  }, [preview])

  const onVideoChange = e => {
    const f = e.target.files && e.target.files[0]
    console.log(f)
    setFile(f || null)
    setStatus('Ready to create')
    if (f) {
      // show preview for video files
      const url = URL.createObjectURL(f)
      setPreview(url)
    } else {
      setPreview(null)
    }
  }

  const submitHnadler = async (e) => {
    e.preventDefault()
    // Use FormData for file upload so the video isn't serialized to an empty object
    const fd = new FormData()
    if (file) fd.append('video', file)
    fd.append('name', name)
    fd.append('discription', description)
    // log summary for debugging
    // (do not log the file object directly in production)
    // console.log({ name, description, fileName: file ? file.name : null, fileType: file ? file.type : null })
    try {
      const response = await axios.post(
        'http://localhost:3000/api/food',
        fd,
        { withCredentials: true }
      )
      console.log(response.data)
      setStatus('Submitted')
      navigate('/')
    } catch (error) {
      console.log(error)
    }
  }

  const handleReset = () => {
    setName('')
    setDescription('')
    setFile(null)
    setPreview(null)
    setStatus('Ready to create')
    if (fileRef.current) fileRef.current.value = null
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-amber-50 to-yellow-50">
      <div className="w-full max-w-md mx-auto">
        <div className="bg-white/90 backdrop-blur-sm shadow-lg">
          {/* Mobile Header */}
          <div className="bg-gradient-to-r from-orange-500 to-amber-500 px-4 py-6 text-white">
            <div className="flex items-center gap-2 mb-1">
              <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2 6a2 2 0 012-2h6l2 2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" />
                </svg>
              </div>
              <h1 className="text-xl font-bold">Create Food</h1>
            </div>
            <p className="text-orange-100 text-sm">Upload video and add details</p>
          </div>

          {/* Mobile Form Content */}
          <div className="px-4 pb-6">
            <form className="space-y-6" onSubmit={e => submitHnadler(e)}>
              {/* Video Upload Section - Mobile Optimized */}
              <div className="pt-6">
                <label className="block text-base font-semibold text-gray-800 mb-3">
                  Food Video
                </label>
                
                {/* Video Preview - Full Width Mobile */}
                <div className="mb-4">
                  <div className="w-full h-48 rounded-xl bg-gradient-to-br from-gray-100 to-gray-200 border-2 border-dashed border-gray-300 flex items-center justify-center">
                    {preview ? (
                      <video src={preview} controls className="w-full h-full object-cover rounded-xl" />
                    ) : (
                      <div className="text-center">
                        <svg className="w-12 h-12 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                        <p className="text-gray-500 text-sm font-medium">Video Preview</p>
                        <p className="text-xs text-gray-400">Upload to see preview</p>
                      </div>
                    )}
                  </div>
                </div>
                
                {/* Upload Controls - Mobile Stack */}
                <div className="bg-gradient-to-br from-orange-50 to-amber-50 rounded-xl p-4 border border-orange-200 mb-4">
                  <p className="text-xs text-gray-600 mb-3">
                    MP4, MOV, or AVI formats. Max 50MB.
                  </p>
                  
                  <div className="space-y-2">
                    <input ref={fileRef} onChange={onVideoChange} type="file" accept="video/*" className="hidden" id="food-video" />
                    <label htmlFor="food-video" className="flex items-center justify-center gap-2 w-full px-4 py-3 bg-gradient-to-r from-orange-500 to-amber-500 text-white font-medium rounded-lg shadow-md active:scale-95 transition-all duration-200 cursor-pointer">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                      </svg>
                      Choose Video
                    </label>
                    
                    <div className="flex gap-2">
                      <button type="button" onClick={() => fileRef.current && fileRef.current.click()} className="flex-1 px-3 py-2 bg-white border border-gray-200 text-gray-700 text-sm font-medium rounded-lg active:scale-95 transition-all duration-200">
                        Browse
                      </button>
                      <button type="button" onClick={() => { setFile(null); setPreview(null); if (fileRef.current) fileRef.current.value = null }} className="px-3 py-2 text-red-500 text-sm font-medium rounded-lg active:scale-95 transition-all duration-200">
                        Remove
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Name Field - Mobile Optimized */}
              <div>
                <label htmlFor="name" className="block text-base font-semibold text-gray-800 mb-2">
                  Food Name
                </label>
                <input 
                  id="name" 
                  value={name} 
                  onChange={e => setName(e.target.value)}
                  type="text" 
                  placeholder="Ex: Spicy Chicken Biryani" 
                  className="block w-full rounded-lg border-2 border-gray-200 px-4 py-3 text-base shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-300 focus:border-orange-400 transition-all duration-200 bg-white"
                />
              </div>

              {/* Description Field - Mobile Optimized */}
              <div>
                <label htmlFor="description" className="block text-base font-semibold text-gray-800 mb-2">
                  Description
                </label>
                <textarea 
                  id="description" 
                  value={description} 
                  onChange={e => setDescription(e.target.value)}
                  rows={3} 
                  placeholder="Describe the flavors and ingredients..."
                  className="block w-full rounded-lg border-2 border-gray-200 px-4 py-3 text-base shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-300 focus:border-orange-400 transition-all duration-200 resize-none bg-white"
                />
              </div>

              {/* Mobile Action Buttons - Stacked */}
              <div className="space-y-3 pt-4">
                <button 
                  type="submit" 
                  className="w-full px-6 py-4 bg-gradient-to-r from-orange-500 to-amber-500 text-white font-semibold rounded-lg shadow-lg active:scale-95 transition-all duration-200 flex items-center justify-center gap-2"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Create Food Item
                </button>
                
                <button 
                  type="button" 
                  onClick={handleReset}
                  className="w-full px-6 py-3 bg-white border-2 border-gray-300 text-gray-700 font-medium rounded-lg active:scale-95 transition-all duration-200 flex items-center justify-center gap-2"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  Reset Form
                </button>
                
                {/* Status indicator */}
                <div className="flex items-center justify-center gap-2 text-sm pt-2">
                  <div className={`w-2 h-2 ${status.includes('Submitted') ? 'bg-blue-500' : 'bg-green-500'} rounded-full`}></div>
                  <span className={`${status.includes('Submitted') ? 'text-blue-600' : 'text-green-600'} font-medium`}>{status}</span>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CreateFood