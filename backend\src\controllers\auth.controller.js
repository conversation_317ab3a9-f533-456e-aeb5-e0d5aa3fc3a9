import userModel from "../model/user.model.js";
import foodPartnerModel from "../model/foodpartner.model.js";
import bcrypt from "bcryptjs";
import jwt from "jsonwebtoken";

export const RegisterUser = async (req, res) => {
  try {
    const { fullName, email, password } = req.body;

    if (!fullName || !email || !password) {
      return res.status(400).json({ message: "All fields are required" });
    }

    const existingUser = await userModel.findOne({ email });
    if (existingUser) {
      return res.status(400).json({ message: "User already exists" });
    }

    const hashedPassword = await bcrypt.hash(password, 10);

    const newUser = new userModel({
      fullName,
      email,
      password: hashedPassword,
    });

    await newUser.save(); // save first

    const token = jwt.sign({ id: newUser._id }, process.env.JWT_SECRET, {
      expiresIn: "7d", // optional but recommended
    });

    res.cookie("token", token);
    res.status(201).json({
      message: "User created successfully",
      user: {
        _id: newUser._id,
        fullName: newUser.fullName,
        email: newUser.email,
      },
    });
  } catch (err) {
    res.status(500).json({ message: "Server error", error: err.message });
  }
};

export const LoginUser = async (req, res) => {
    const {email , password} = req.body;

    if(!email || !password){
        return res.status(400).json({message : "All fields are required"});
    }

    const isUserRigesterd = await userModel.findOne({email});
    if(!isUserRigesterd){
        return res.status(400).json({message : "User not found . Please Check your Email and Password"});
    }
    const isPasswordValid  = await bcrypt.compare(password , isUserRigesterd.password);
    if(!isPasswordValid){
        return res.status(400).json({message : "User not found . Please Check your Email and Password"});
    }
    const token = jwt.sign({id : isUserRigesterd._id} , process.env.JWT_SECRET , {
        expiresIn : "7d"
    });
    res.cookie("token" , token , {httpOnly : true});
    res.status(200).json({
        message : "Login successful",
        user : {
            _id : isUserRigesterd._id,
            fullName : isUserRigesterd.fullName,
            email : isUserRigesterd.email,
        }
    })
}

export const LogoutUser = (req ,res)=>{
    res.clearCookie("token");
    res.status(200).json({message : "Logout successful"});
}

export const RegisterFoodPartneder = async (req, res) => {
    try {
    const { name, contactName , phone , address , email , password  } = req.body;

    if (!name || !email || !password || !contactName || !phone || !address) {
      return res.status(400).json({ message: "All fields are required" });
    }

    const existingFoodPartner = await foodPartnerModel.findOne({ email });
    if (existingFoodPartner) {
      return res.status(400).json({ message: "User already exists" });
    }

    const hashedPassword = await bcrypt.hash(password, 10);

    const newFoodPartner = new foodPartnerModel({
      name,
      contactName,
      phone,
      address,
      email,
      password: hashedPassword,
    });

    await newFoodPartner.save(); // save first

    const token = jwt.sign({ id: newFoodPartner._id }, process.env.JWT_SECRET, {
      expiresIn: "7d", // optional but recommended
    });

    res.cookie("token", token);
    res.status(201).json({
      message: "User created successfully",
      user: {
        _id: newFoodPartner._id,
        name: newFoodPartner.name,
        contactName: newFoodPartner.contactName,
        phone: newFoodPartner.phone,
        address: newFoodPartner.address,
        email: newFoodPartner.email,
        
      },
    });
  } catch (err) {
    res.status(500).json({ message: "Server error", error: err.message });
  }
}

export const LoginFoodPartneder = async (req, res) => {
    const {email , password}  = req.body;
    if(!email || !password){
        return res.status(400).json({message : "All fields are required"});
    }
    const isFoodPartnerRegistered  = await foodPartnerModel.findOne({email});
    if(!isFoodPartnerRegistered){
        return res.status(400).json({message : "User not found . Please Check your Email and Password"});
    }
    const isPasswordValid  = await bcrypt.compare(password , isFoodPartnerRegistered.password);
    if(!isPasswordValid){
        return res.status(400).json({message : "User not found . Please Check your Email and Password"});
    }
    const token = jwt.sign({id : isFoodPartnerRegistered._id} , process.env.JWT_SECRET , {
        expiresIn : "7d"
    });
    res.cookie("token" , token , {httpOnly : true});
    res.status(200).json({
        message : "Login successful",
        user : {
            _id : isFoodPartnerRegistered._id,
            name : isFoodPartnerRegistered.name,
            email : isFoodPartnerRegistered.email,
        }
    })
}

export const LogoutFoodPartneder = (req ,res)=>{
    res.clearCookie("token");
    res.status(200).json({message : "Logout successful"});
}